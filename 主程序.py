import sys
import time
import threading
import os
from typing import Dict, List, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from 模块.数据采集 import 数据采集器
from 模块.界面 import 创建界面
from 模块.配置管理 import 配置管理器
from 模块.Cookie管理 import Cookie管理器

class 主程序:
    def __init__(self):
        # 创建必要的文件夹结构
        self.创建文件夹结构()
        
        # 初始化组件
        self.配置管理器 = 配置管理器()
        self.配置管理器.设置配置目录(self.配置目录)
        
        self.cookie管理器 = Cookie管理器()
        self.cookie管理器.设置配置目录(self.配置目录)
        
        self.数据采集器 = 数据采集器()
        self.数据采集器.设置Cookie管理器(self.cookie管理器)
        
        self.界面 = 创建界面(self)
        self.采集结果 = []
        self.采集状态 = "空闲"
        self.当前卖家ID = ""
        
    def 创建文件夹结构(self):
        """创建项目所需的文件夹结构"""
        # 定义文件夹路径
        self.项目根目录 = os.path.dirname(os.path.abspath(__file__))
        self.数据目录 = os.path.join(self.项目根目录, "数据")
        self.卖家数据目录 = os.path.join(self.数据目录, "卖家数据")
        self.配置目录 = os.path.join(self.项目根目录, "配置")
        self.日志目录 = os.path.join(self.项目根目录, "日志")

        # 创建文件夹
        for 目录 in [self.数据目录, self.卖家数据目录, self.配置目录, self.日志目录]:
            if not os.path.exists(目录):
                os.makedirs(目录)
                
        # 移动现有配置文件到配置目录
        self.移动现有文件()
        
    def 移动现有文件(self):
        """移动现有文件到对应目录"""
        # 移动配置文件
        源文件列表 = [
            ('闲鱼配置.ini', self.配置目录),
            ('cookie_config.json', self.配置目录),
            ('api_keys.txt', self.配置目录)
        ]

        for 源文件, 目标目录 in 源文件列表:
            源路径 = os.path.join(self.项目根目录, 源文件)
            目标路径 = os.path.join(目标目录, 源文件)

            if os.path.exists(源路径) and not os.path.exists(目标路径):
                try:
                    # 复制文件而不是移动，避免破坏现有功能
                    with open(源路径, 'r', encoding='utf-8') as 源:
                        with open(目标路径, 'w', encoding='utf-8') as 目标:
                            目标.write(源.read())
                    print(f"已复制文件 {源文件} 到 {目标目录}")
                except Exception as e:
                    print(f"复制文件 {源文件} 时出错: {e}")

        # 移动卖家数据文件
        for 文件名 in os.listdir(self.项目根目录):
            if 文件名.startswith("卖家_") and 文件名.endswith(".json"):
                源路径 = os.path.join(self.项目根目录, 文件名)
                目标路径 = os.path.join(self.卖家数据目录, 文件名)

                if not os.path.exists(目标路径):
                    try:
                        # 复制文件而不是移动
                        with open(源路径, 'r', encoding='utf-8') as 源:
                            with open(目标路径, 'w', encoding='utf-8') as 目标:
                                目标.write(源.read())
                        print(f"已复制文件 {文件名} 到 {self.卖家数据目录}")
                    except Exception as e:
                        print(f"复制文件 {文件名} 时出错: {e}")
        
    def 开始采集(self, 卖家ID: str, 页数: int, 只采集有想要: bool, 只保存ID: bool) -> None:
        """开始采集数据"""
        if self.采集状态 == "运行中":
            return
        
        self.采集状态 = "运行中"
        self.当前卖家ID = 卖家ID
        self.采集结果 = []
        
        # 创建一个新线程来执行采集
        采集线程 = threading.Thread(
            target=self._执行采集,
            args=(卖家ID, 页数, 只采集有想要, 只保存ID)
        )
        采集线程.daemon = True
        采集线程.start()
    
    def _执行采集(self, 卖家ID: str, 页数: int, 只采集有想要: bool, 只保存ID: bool) -> None:
        """在线程中执行采集"""
        try:
            self.界面.更新状态(f"开始采集卖家 {卖家ID} 的商品数据...")
            
            采集结果 = []
            
            # 如果页数为0，表示采集全部页面
            当前页码 = 1
            最大页数 = 页数 if 页数 > 0 else float('inf')  # 如果页数为0，设置一个无限大的值
            
            while 当前页码 <= 最大页数:
                结果 = self.数据采集器.爬取卖家页面(当前页码, 卖家ID)
                
                if isinstance(结果, str):
                    if 结果 == "NO_MORE_DATA":
                        self.界面.更新状态(f"已到达最后一页，共采集 {当前页码-1} 页")
                        break
                    elif 结果 == "COOKIE_EXPIRED" or 结果 == "COOKIE_ERROR":
                        self.界面.更新状态("Cookie已过期或无效，请更新Cookie")
                        break
                    elif 结果 == "LIMIT_REACHED":
                        self.界面.更新状态(f"已达到最大可查看页数限制，共采集 {当前页码-1} 页")
                        break
                    else:
                        self.界面.更新状态(f"采集第 {当前页码} 页时出错: {结果}")
                        当前页码 += 1
                        continue
                
                # 过滤数据（如果需要）
                if 只采集有想要:
                    结果 = [商品 for 商品 in 结果 if 'want_count' in 商品]
                
                采集结果.extend(结果)
                
                self.界面.更新状态(f"已采集第 {当前页码} 页，当前共有 {len(采集结果)} 个商品")
                当前页码 += 1
                # 移除等待时间，实现快速采集
            
            if len(采集结果) > 0:
                # 保存为JSON格式
                文件路径 = self.保存采集数据(采集结果, 卖家ID)
                self.界面.更新状态(f"商品数据已保存到: {文件路径}")
                self.界面.更新状态(f"采集完成，共采集到 {len(采集结果)} 个商品")
            else:
                self.界面.更新状态("采集完成，但未获取到任何商品数据")
            
        except Exception as e:
            self.界面.更新状态(f"采集过程中出现错误: {e}")
        finally:
            self.采集状态 = "空闲"
            
    def 开始批量采集(self, 卖家ID列表: List[str], 页数: int, 只采集有想要: bool, 只保存ID: bool, 使用多线程: bool = True) -> None:
        """开始批量采集多个卖家的数据"""
        if self.采集状态 == "运行中":
            return

        self.采集状态 = "运行中"

        # 创建一个新线程来执行批量采集
        采集线程 = threading.Thread(
            target=self._执行批量采集,
            args=(卖家ID列表, 页数, 只采集有想要, 只保存ID, 使用多线程)
        )
        采集线程.daemon = True
        采集线程.start()
        
    def _执行批量采集(self, 卖家ID列表: List[str], 页数: int, 只采集有想要: bool, 只保存ID: bool, 使用多线程: bool = True) -> None:
        """在线程中执行批量采集"""
        try:
            总卖家数 = len(卖家ID列表)

            if 使用多线程:
                self.界面.更新状态(f"开始多线程批量采集 {总卖家数} 个卖家的商品数据...")
                self._执行多线程批量采集(卖家ID列表, 页数, 只采集有想要, 只保存ID)
            else:
                self.界面.更新状态(f"开始单线程批量采集 {总卖家数} 个卖家的商品数据...")
                self._执行单线程批量采集(卖家ID列表, 页数, 只采集有想要, 只保存ID)

        except Exception as e:
            self.界面.更新状态(f"批量采集过程中出现错误: {e}")
        finally:
            self.采集状态 = "空闲"

    def _执行多线程批量采集(self, 卖家ID列表: List[str], 页数: int, 只采集有想要: bool, 只保存ID: bool) -> None:
        """执行多线程批量采集"""
        总卖家数 = len(卖家ID列表)

        # 创建线程锁用于同步状态更新
        状态锁 = threading.Lock()
        完成计数 = {"count": 0}

        # 使用线程池执行器进行并发采集
        最大线程数 = min(总卖家数, 8)  # 最多8个线程同时运行，避免过多请求

        with ThreadPoolExecutor(max_workers=最大线程数) as executor:
            # 为每个卖家创建独立的数据采集器实例
            future_to_seller = {}

            for 卖家ID in 卖家ID列表:
                if self.采集状态 != "运行中":
                    break

                # 创建独立的数据采集器实例
                采集器 = 数据采集器()
                采集器.设置Cookie管理器(self.cookie管理器)

                # 提交采集任务
                future = executor.submit(
                    self._执行单个卖家采集,
                    卖家ID, 页数, 只采集有想要, 只保存ID, 采集器, 状态锁, 完成计数, 总卖家数
                )
                future_to_seller[future] = 卖家ID

            # 等待所有任务完成
            for future in as_completed(future_to_seller):
                卖家ID = future_to_seller[future]
                try:
                    future.result()  # 获取结果，如果有异常会抛出
                except Exception as e:
                    with 状态锁:
                        self.界面.更新状态(f"卖家 {卖家ID} 采集失败: {e}")

                # 检查是否被用户停止
                if self.采集状态 != "运行中":
                    self.界面.更新状态("采集已被用户停止")
                    break

        self.界面.更新状态("多线程批量采集完成")

    def _执行单线程批量采集(self, 卖家ID列表: List[str], 页数: int, 只采集有想要: bool, 只保存ID: bool) -> None:
        """执行单线程批量采集（原有逻辑）"""
        总卖家数 = len(卖家ID列表)

        for 索引, 卖家ID in enumerate(卖家ID列表):
            if self.采集状态 != "运行中":
                self.界面.更新状态("采集已被用户停止")
                break

            self.界面.更新状态(f"正在采集第 {索引+1}/{总卖家数} 个卖家 (ID: {卖家ID})...")
            self.当前卖家ID = 卖家ID

            # 执行单个卖家的采集
            self._执行采集(卖家ID, 页数, 只采集有想要, 只保存ID)

        self.界面.更新状态("单线程批量采集完成")

    def _执行单个卖家采集(self, 卖家ID: str, 页数: int, 只采集有想要: bool, 只保存ID: bool,
                        采集器: 数据采集器, 状态锁: threading.Lock, 完成计数: Dict, 总卖家数: int) -> None:
        """在多线程环境中执行单个卖家的采集"""
        try:
            with 状态锁:
                self.界面.更新状态(f"开始采集卖家 {卖家ID} 的商品数据...")

            采集结果 = []

            # 如果页数为0，表示采集全部页面
            当前页码 = 1
            最大页数 = 页数 if 页数 > 0 else float('inf')

            while 当前页码 <= 最大页数:
                # 检查是否被用户停止
                if self.采集状态 != "运行中":
                    break

                结果 = 采集器.爬取卖家页面(当前页码, 卖家ID)

                if isinstance(结果, str):
                    if 结果 == "NO_MORE_DATA":
                        with 状态锁:
                            self.界面.更新状态(f"卖家 {卖家ID}: 已到达最后一页，共采集 {当前页码-1} 页")
                        break
                    elif 结果 == "COOKIE_EXPIRED" or 结果 == "COOKIE_ERROR":
                        with 状态锁:
                            self.界面.更新状态(f"卖家 {卖家ID}: Cookie已过期或无效")
                        break
                    elif 结果 == "LIMIT_REACHED":
                        with 状态锁:
                            self.界面.更新状态(f"卖家 {卖家ID}: 已达到最大可查看页数限制，共采集 {当前页码-1} 页")
                        break
                    else:
                        with 状态锁:
                            self.界面.更新状态(f"卖家 {卖家ID}: 采集第 {当前页码} 页时出错: {结果}")
                        当前页码 += 1
                        continue

                # 过滤数据（如果需要）
                if 只采集有想要:
                    结果 = [商品 for 商品 in 结果 if 'want_count' in 商品]

                采集结果.extend(结果)

                with 状态锁:
                    self.界面.更新状态(f"卖家 {卖家ID}: 已采集第 {当前页码} 页，当前共有 {len(采集结果)} 个商品")
                当前页码 += 1

            if len(采集结果) > 0:
                # 保存为JSON格式
                文件路径 = self.保存采集数据(采集结果, 卖家ID)
                with 状态锁:
                    完成计数["count"] += 1
                    self.界面.更新状态(f"卖家 {卖家ID}: 采集完成，共采集到 {len(采集结果)} 个商品 ({完成计数['count']}/{总卖家数})")
                    self.界面.更新状态(f"卖家 {卖家ID}: 数据已保存到: {文件路径}")
            else:
                with 状态锁:
                    完成计数["count"] += 1
                    self.界面.更新状态(f"卖家 {卖家ID}: 采集完成，但未获取到任何商品数据 ({完成计数['count']}/{总卖家数})")

        except Exception as e:
            with 状态锁:
                完成计数["count"] += 1
                self.界面.更新状态(f"卖家 {卖家ID}: 采集过程中出现错误: {e} ({完成计数['count']}/{总卖家数})")
            
    def 一键采集全部卖家(self, 页数: int, 只采集有想要: bool, 只保存ID: bool, 使用多线程: bool = True) -> None:
        """一键采集所有卖家的数据

        Args:
            页数: 采集页数，0表示全部页面
            只采集有想要: 是否只采集有想要数量的商品
            只保存ID: 是否只保存商品ID（暂未使用）
            使用多线程: 是否使用多线程并发采集，默认True
        """
        卖家列表 = self.配置管理器.获取所有卖家()
        卖家ID列表 = [卖家['卖家ID'] for 卖家 in 卖家列表]

        if not 卖家ID列表:
            self.界面.更新状态("没有找到卖家信息，请先添加卖家")
            return

        if 使用多线程:
            self.界面.更新状态(f"共加载了 {len(卖家ID列表)} 个卖家，将使用多线程并发采集")
        else:
            self.界面.更新状态(f"共加载了 {len(卖家ID列表)} 个卖家，将使用单线程顺序采集")

        self.开始批量采集(卖家ID列表, 页数, 只采集有想要, 只保存ID, 使用多线程)

    def 保存采集数据(self, 采集结果: List[Dict], 卖家ID: str) -> str:
        """保存采集数据到JSON文件"""
        import json
        from datetime import datetime

        # 获取卖家名称
        卖家名称 = self.配置管理器.获取卖家名称(卖家ID) or "未知卖家"

        # 构造保存数据
        保存数据 = {
            "卖家信息": {
                "卖家ID": 卖家ID,
                "卖家名称": 卖家名称,
                "采集时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "商品总数": len(采集结果)
            },
            "商品列表": 采集结果
        }

        # 保存到文件
        文件名 = f"卖家_{卖家ID}.json"
        文件路径 = os.path.join(self.卖家数据目录, 文件名)

        with open(文件路径, 'w', encoding='utf-8') as f:
            json.dump(保存数据, f, ensure_ascii=False, indent=2)

        return 文件路径

    def 获取文件路径(self, 文件类型: str, 文件名: str) -> str:
        """根据文件类型获取文件的完整路径"""
        if 文件类型 == "卖家数据":
            return os.path.join(self.卖家数据目录, 文件名)
        elif 文件类型 == "配置":
            return os.path.join(self.配置目录, 文件名)
        elif 文件类型 == "日志":
            return os.path.join(self.日志目录, 文件名)
        else:
            return os.path.join(self.项目根目录, 文件名)

if __name__ == "__main__":
    app = 主程序()
    app.界面.root.mainloop()  # 修改这里，调用root对象的mainloop方法 
    