import requests
import json
import time
import hashlib
import re
import random
from typing import Dict, List, Union, Tuple, Any
from .config_manager import Config<PERSON>anager
from .cookie_manager import CookieManager

class DataCollector:
    def __init__(self):
        self.config = None
        self.cookie_manager = None
        self.system_settings = None
        self.user_agent = None
        self.app_key = None
        self.api_name = None
        self.base_url = None
        self.cookie = None
        
    def set_cookie_manager(self, cookie_manager: CookieManager) -> None:
        """设置Cookie管理器"""
        self.cookie_manager = cookie_manager
        self.cookie = self.cookie_manager.get_cookie()

        # 初始化配置管理器
        self.config = ConfigManager()
        self.config.set_config_directory(self.cookie_manager.config_directory)

        # 获取系统设置
        self.system_settings = self.config.get_system_settings()
        self.user_agent = self.system_settings["用户代理"]
        self.app_key = self.system_settings["应用密钥"]
        self.api_name = self.system_settings["接口名称"]
        self.base_url = self.system_settings["基础URL"]
        
    def get_signature(self, token: str, t: int, app_key: str, data: str) -> str:
        return hashlib.md5(f"{token}&{t}&{app_key}&{data}".encode()).hexdigest()
        
    def clean_text(self, text: Any) -> str:
        if not isinstance(text, str):
            return str(text)
        return text.replace('\n', ' ').replace('\r', '')
        
    def parse_want_count(self, card_data: Dict) -> Tuple[bool, int]:
        try:
            label_data = card_data.get('itemLabelDataVO', {}).get('labelData', {})
            r3_data = label_data.get('r3', {})
            tag_list = r3_data.get('tagList', [])
            if tag_list and len(tag_list) > 0:
                content = tag_list[0].get('data', {}).get('content', '0人想要')
                if '想要' in content:
                    num = re.search(r'(\d+)', content)
                    if num:
                        return True, int(num.group(1))
            return False, 0
        except:
            return False, 0
            
    def crawl_seller_page(self, page_number: int, seller_id: str, group_id: int = 51959993, group_name: str = "综合", retry: bool = True) -> Union[List[Dict], str]:
        print(f"正在爬取卖家 {seller_id} 的第{page_number}页...")
        
        data_params = {
            "pageNumber": page_number,
            "pageSize": 20,
            "userId": seller_id,
            "groupId": group_id,
            "groupName": group_name,
            "needGroupInfo": False,
            "defaultGroup": True,
            "nextPageModel": "recommend"
        }
        
        data_str = json.dumps(data_params, separators=(',', ':'))
        t = int(time.time() * 1000)
        
        try:
            token = self.cookie.split("_m_h5_tk=")[1].split("_")[0]
        except (IndexError, ValueError):
            print("Cookie格式不正确，无法提取token")
            if retry:
                new_cookie = self.cookie_manager.refresh_cookie()
                if new_cookie:
                    self.cookie = new_cookie
                    # 无需等待，立即重试
                    return self.crawl_seller_page(page_number, seller_id, group_id, group_name, retry=False)
            return "COOKIE_ERROR"
        
        signature = self.get_signature(token, t, self.app_key, data_str)
        
        params = {
            'jsv': '2.7.2',
            'appKey': self.app_key,
            't': t,
            'sign': signature,
            'v': '1.0',
            'api': self.api_name,
            'type': 'originaljson',
            'dataType': 'json',
            'data': data_str,
            'accountSite': 'xianyu',
            'timeout': '20000'
        }
        
        headers = {
            'cookie': self.cookie,
            'user-agent': self.user_agent
        }
        
        results = []
        
        try:
            response = requests.get(self.base_url, params=params, headers=headers)
            response.raise_for_status()
            data = response.json()
            
            if 'ret' in data:
                if not any("SUCCESS" in ret_str for ret_str in data['ret']):
                    error_msg = f"API调用失败: {data['ret']}"
                    
                    if any("令牌过期" in ret_str for ret_str in data['ret']) or any("TOKEN_EMPTY" in ret_str for ret_str in data['ret']):
                        error_msg = "Cookie已过期，请更新Cookie"
                        print(f"错误: {error_msg}")
                        
                        if retry:
                            new_cookie = self.cookie_manager.refresh_cookie()
                            if new_cookie:
                                self.cookie = new_cookie
                                # 无需等待，立即重试
                                return self.crawl_seller_page(page_number, seller_id, group_id, group_name, retry=False)
                        return "COOKIE_EXPIRED"
                        
                    elif any("签名错误" in ret_str for ret_str in data['ret']):
                        error_msg = "签名计算出错，请检查签名算法"
                    elif any("调用次数超限" in ret_str for ret_str in data['ret']):
                        error_msg = "请求频率过高，被限制"
                    elif any("最大可查看页数或者每页最大可查看商品数超限" in ret_str for ret_str in data['ret']):
                        error_msg = "已到达最大可查看页数限制"
                        print(f"提示: {error_msg}")
                        return "LIMIT_REACHED"
                    
                    print(f"错误: {error_msg}")
                    if retry:
                        print("尝试重新获取Cookie...")
                        new_cookie = self.cookie_manager.refresh_cookie()
                        if new_cookie:
                            self.cookie = new_cookie
                            # 无需等待，立即重试
                            return self.crawl_seller_page(page_number, seller_id, group_id, group_name, retry=False)
                    return "API_ERROR"
            
            if 'data' not in data or not data['data']:
                print("没有更多数据了")
                return "NO_MORE_DATA"
                
            if 'cardList' not in data['data']:
                print("没有更多数据了")
                return "NO_MORE_DATA"
                
            if not data['data']['cardList']:
                print("没有更多数据了")
                return "NO_MORE_DATA"
            
            for card in data['data']['cardList']:
                try:
                    card_data = card.get('cardData', {})
                    title = self.clean_text(card_data.get('title', '未知标题'))
                    item_id = card_data.get('id', '未知ID')
                    has_want, want_count = self.parse_want_count(card_data)
                    
                    if has_want:
                        result = {
                            'title': title,
                            'id': item_id,
                            'want_count': want_count
                        }
                    else:
                        result = {
                            'title': title,
                            'id': item_id
                        }
                    results.append(result)
                except Exception:
                    continue
            
            print(f"第{page_number}页成功获取到{len(results)}条商品信息")
            return results
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            if retry:
                print("尝试重新获取Cookie...")
                new_cookie = self.cookie_manager.refresh_cookie()
                if new_cookie:
                    self.cookie = new_cookie
                    # 无需等待，立即重试
                    return self.crawl_seller_page(page_number, seller_id, group_id, group_name, retry=False)
            return "REQUEST_ERROR"
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            return "JSON_ERROR"
        except Exception as e:
            print(f"发生未知错误: {e}")
            return "UNKNOWN_ERROR"
