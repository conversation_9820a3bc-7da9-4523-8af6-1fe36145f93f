import os
import json
from typing import Optional

# 尝试导入playwright，如果失败则设置标志
try:
    from playwright.sync_api import sync_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False

from .config_manager import ConfigManager

class CookieManager:
    def __init__(self):
        self.config = None
        self.cookie = ""  # 默认为空字符串而不是None
        self.config_directory = os.path.dirname(os.path.abspath(__file__))
        self.cookie_config_file = os.path.join(self.config_directory, 'cookie_config.json')
        
    def set_config_directory(self, config_directory: str) -> None:
        """设置配置文件保存目录"""
        self.config_directory = config_directory
        self.cookie_config_file = os.path.join(self.config_directory, 'cookie_config.json')

        # 初始化配置管理器
        self.config = ConfigManager()
        self.config.set_config_directory(self.config_directory)
        self.cookie = self.config.get_cookie()
        
    def get_cookie(self) -> str:
        """从配置文件获取Cookie"""
        try:
            # 如果配置未初始化，先尝试从配置管理器获取
            if self.config is not None:
                return self.config.get_cookie()

            # 否则从cookie配置文件获取
            if os.path.exists(self.cookie_config_file):
                with open(self.cookie_config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get('cookie', '')
            return ''
        except Exception as e:
            print(f"加载cookie配置文件失败: {e}")
            return ''
            
    def save_cookie(self, cookie: str) -> bool:
        """保存Cookie到配置文件"""
        try:
            with open(self.cookie_config_file, 'w', encoding='utf-8') as f:
                json.dump({'cookie': cookie}, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"保存cookie配置文件失败: {e}")
            return False
            
    def refresh_cookie(self, need_login: bool = False) -> Optional[str]:
        """使用浏览器自动获取新Cookie

        Args:
            need_login: 是否需要登录获取完整Cookie，默认False（仅访问页面获取基础Cookie）
        """
        if not PLAYWRIGHT_AVAILABLE:
            print("错误: playwright未安装，无法使用自动Cookie获取功能")
            print("请运行: pip install playwright && playwright install chromium")
            return None

        print("正在启动浏览器获取Cookie...")
        if need_login:
            print("模式: 需要登录获取完整Cookie")
        else:
            print("模式: 仅访问页面获取基础Cookie")

        try:
            with sync_playwright() as p:
                # 启动浏览器
                print("正在启动Chrome浏览器...")
                browser = p.chromium.launch(
                    headless=False,  # 显示浏览器窗口
                    args=[
                        '--disable-blink-features=AutomationControlled',
                        '--disable-web-security',
                        '--disable-features=VizDisplayCompositor'
                    ]
                )

                context = browser.new_context(
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                    viewport={'width': 1280, 'height': 720}
                )

                page = context.new_page()

                # 访问闲鱼页面
                print("正在访问闲鱼页面...")
                page.goto("https://2.taobao.com/", wait_until='networkidle')

                if need_login:
                    print("请在浏览器中完成登录...")
                    print("登录完成后，请关闭浏览器窗口")
                    
                    # 等待用户手动登录
                    try:
                        page.wait_for_url("**/2.taobao.com/**", timeout=300000)  # 等待5分钟
                    except:
                        print("等待超时或用户关闭了浏览器")
                else:
                    print("正在获取基础Cookie...")
                    # 等待页面加载完成
                    page.wait_for_timeout(3000)

                # 获取所有Cookie
                cookies = context.cookies()

                # 构建Cookie字符串
                cookie_str = ""
                important_cookies = ['_m_h5_tk', 'cookie2', '_tb_token_', 'sgcookie', 'unb', 'uc1', 'uc3', 'uc4', 'mtop_partitioned_detect']

                for cookie in cookies:
                    # 获取所有闲鱼相关域名的Cookie
                    if (cookie['domain'] in ['.taobao.com', '2.taobao.com', '.goofish.com', 'h5api.m.goofish.com'] or
                        cookie['name'] in important_cookies):
                        cookie_str += f"{cookie['name']}={cookie['value']}; "

                # 移除末尾的分号和空格
                cookie_str = cookie_str.rstrip('; ')

                # 关闭浏览器
                browser.close()
                print("浏览器已关闭")

                if cookie_str:
                    # 检查是否包含基本的Cookie字段
                    has_basic_cookies = any(field in cookie_str for field in ['cookie2=', 'mtop_partitioned_detect='])

                    if has_basic_cookies:
                        # 保存新的Cookie
                        if self.save_cookie(cookie_str):
                            print("Cookie获取并保存成功！")
                            print(f"Cookie长度: {len(cookie_str)} 字符")

                            # 显示获取到的主要Cookie字段
                            if '_m_h5_tk=' in cookie_str:
                                print("✓ 包含_m_h5_tk字段（API调用令牌）")
                            if 'cookie2=' in cookie_str:
                                print("✓ 包含cookie2字段（基础认证）")
                            if 'mtop_partitioned_detect=' in cookie_str:
                                print("✓ 包含mtop_partitioned_detect字段")

                            return cookie_str
                        else:
                            print("Cookie保存失败")
                            return None
                    else:
                        print("获取到的Cookie缺少必要字段，可能需要刷新页面或稍后重试")
                        return None
                else:
                    print("未能获取到任何Cookie")
                    return None

        except Exception as e:
            print(f"获取Cookie时发生错误: {e}")
            return None
