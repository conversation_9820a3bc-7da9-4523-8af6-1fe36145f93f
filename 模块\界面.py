import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, IntVar, BooleanVar, Text
import threading
import time
import random
from typing import List, Dict, Any, Optional
import os
from datetime import datetime
import json

from .配置管理 import 配置管理器
from .Cookie管理 import Cookie管理器
from .数据采集 import 数据采集器

class 闲鱼采集界面:
    def __init__(self, root, 主程序=None):
        self.root = root
        self.root.title("闲鱼卖家商品采集工具")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)

        # 设置最小窗口大小，避免布局错乱
        self.root.minsize(800, 600)

        # 初始化变量
        self.主程序 = 主程序  # 主程序实例的引用

        # 创建样式
        self.创建样式()

        # 如果有主程序实例，使用主程序的组件，否则创建自己的组件
        if 主程序:
            self.配置 = 主程序.配置管理器
            self.cookie管理 = 主程序.cookie管理器
            self.数据采集 = 主程序.数据采集器
        else:
            # 兼容性：如果没有主程序实例，创建自己的组件
            self.配置 = 配置管理器()
            self.cookie管理 = Cookie管理器()
            self.数据采集 = 数据采集器()
        
        self.默认设置 = self.配置.获取默认设置()
        self.卖家列表 = self.配置.获取卖家列表()
        
        self.当前卖家ID = tk.StringVar(value=self.默认设置["默认卖家ID"])
        self.当前卖家名称 = tk.StringVar(value=self.默认设置["默认卖家名称"])
        self.当前分组ID = tk.StringVar(value=self.默认设置["默认分组ID"])
        self.当前分组名称 = tk.StringVar(value=self.默认设置["默认分组名称"])

        # 添加缺失的采集选项变量
        self.只采集有想要 = BooleanVar(value=False)
        self.只保存ID = BooleanVar(value=False)

        self.爬取状态 = False
        self.爬取线程 = None
        
        self.创建界面()
        
    def 创建样式(self):
        """创建界面样式"""
        style = ttk.Style()
        
        # 创建交替行的样式
        style.configure('EvenRow.TFrame', background='#f0f0f0')
        style.configure('OddRow.TFrame', background='#ffffff')
        
        # 创建按钮样式
        style.configure('采集.TButton', foreground='blue')
        style.configure('删除.TButton', foreground='red')
        
    def 创建界面(self):
        self.创建菜单栏()
        
        # 创建主框架
        主框架 = ttk.Frame(self.root, padding="10")
        主框架.pack(fill=tk.BOTH, expand=True)
        
        # 创建选项卡
        self.选项卡 = ttk.Notebook(主框架)
        self.选项卡.pack(fill=tk.BOTH, expand=True)
        
        # 创建各个选项卡页面
        self.采集页面 = ttk.Frame(self.选项卡)
        self.设置页面 = ttk.Frame(self.选项卡)
        
        self.选项卡.add(self.采集页面, text="数据采集")
        self.选项卡.add(self.设置页面, text="系统设置")
        
        # 创建各个页面的内容
        self.创建采集页面()
        self.创建设置页面()
    
    def 创建菜单栏(self):
        菜单栏 = tk.Menu(self.root)
        self.root.config(menu=菜单栏)
        
        文件菜单 = tk.Menu(菜单栏, tearoff=0)
        菜单栏.add_cascade(label="文件", menu=文件菜单)
        文件菜单.add_command(label="刷新Cookie", command=self.刷新Cookie)
        文件菜单.add_separator()
        文件菜单.add_command(label="退出", command=self.root.quit)
        
        卖家菜单 = tk.Menu(菜单栏, tearoff=0)
        菜单栏.add_cascade(label="卖家管理", menu=卖家菜单)
        卖家菜单.add_command(label="添加卖家", command=self.打开添加卖家窗口)
        卖家菜单.add_command(label="删除卖家", command=self.删除当前卖家)
        卖家菜单.add_command(label="设为默认", command=self.设置为默认卖家)
        
        帮助菜单 = tk.Menu(菜单栏, tearoff=0)
        菜单栏.add_cascade(label="帮助", menu=帮助菜单)
        帮助菜单.add_command(label="关于", command=self.显示关于信息)
        
    def 创建采集页面(self):
        """创建数据采集页面"""
        # 创建主框架
        主框架 = ttk.Frame(self.采集页面, padding="10")
        主框架.pack(fill=tk.BOTH, expand=True)
        
        # 创建卖家管理框架
        卖家框架 = ttk.LabelFrame(主框架, text="卖家管理", padding="10")
        卖家框架.pack(fill=tk.BOTH, expand=False, pady=10)
        
        # 使用Treeview显示卖家列表
        列表框架 = ttk.Frame(卖家框架)
        列表框架.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 创建Treeview和滚动条
        滚动条 = ttk.Scrollbar(列表框架)
        滚动条.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.卖家列表视图 = ttk.Treeview(列表框架, columns=("卖家名称", "卖家ID"), show="headings", height=6)
        self.卖家列表视图.pack(fill=tk.BOTH, expand=True)
        
        # 设置列宽和标题
        self.卖家列表视图.column("卖家名称", width=250, anchor=tk.W)
        self.卖家列表视图.column("卖家ID", width=150, anchor=tk.W)
        
        self.卖家列表视图.heading("卖家名称", text="卖家名称")
        self.卖家列表视图.heading("卖家ID", text="卖家ID")
        
        # 设置滚动条
        滚动条.config(command=self.卖家列表视图.yview)
        self.卖家列表视图.config(yscrollcommand=滚动条.set)
        
        # 添加卖家区域
        添加卖家框架 = ttk.Frame(卖家框架)
        添加卖家框架.pack(fill=tk.X, pady=10)
        
        ttk.Label(添加卖家框架, text="卖家ID:").grid(row=0, column=0, padx=5, pady=5)
        self.卖家ID输入 = ttk.Entry(添加卖家框架, width=15)
        self.卖家ID输入.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(添加卖家框架, text="卖家名称:").grid(row=0, column=2, padx=5, pady=5)
        self.卖家名称输入 = ttk.Entry(添加卖家框架, width=15)
        self.卖家名称输入.grid(row=0, column=3, padx=5, pady=5)
        
        添加按钮 = ttk.Button(添加卖家框架, text="添加卖家", command=self.添加卖家)
        添加按钮.grid(row=0, column=4, padx=10, pady=5)
        
        # 创建日志区域
        日志框架 = ttk.LabelFrame(主框架, text="运行日志", padding="10")
        日志框架.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.日志文本框 = scrolledtext.ScrolledText(日志框架, wrap=tk.WORD, height=10)
        self.日志文本框.pack(fill=tk.BOTH, expand=True)
        self.日志文本框.config(state=tk.DISABLED)
        
        # 创建操作按钮区域
        按钮框架 = ttk.Frame(主框架)
        按钮框架.pack(fill=tk.X, pady=10)
        
        self.一键采集按钮 = ttk.Button(按钮框架, text="一键采集全部卖家", command=self.一键采集全部卖家)
        self.一键采集按钮.pack(side=tk.LEFT, padx=5)
        
        self.开始按钮 = ttk.Button(按钮框架, text="采集选中卖家", command=self.开始批量采集)
        self.开始按钮.pack(side=tk.LEFT, padx=5)
        
        self.停止按钮 = ttk.Button(按钮框架, text="停止采集", command=self.停止采集, state=tk.DISABLED)
        self.停止按钮.pack(side=tk.LEFT, padx=5)
        
        清空日志按钮 = ttk.Button(按钮框架, text="清空日志", command=self.清空日志)
        清空日志按钮.pack(side=tk.LEFT, padx=5)
        
        打开文件夹按钮 = ttk.Button(按钮框架, text="打开输出文件夹", command=self.打开输出文件夹)
        打开文件夹按钮.pack(side=tk.RIGHT, padx=5)
        
        # 绑定卖家列表视图的事件
        self.卖家列表视图.bind("<Button-3>", self.显示卖家操作菜单)  # 绑定右键点击事件
        self.卖家列表视图.bind("<Double-1>", self.双击开始采集)  # 绑定双击事件
        
        # 初始化卖家列表
        self.刷新卖家列表()
    
    def 刷新卖家列表(self):
        """刷新卖家列表"""
        # 清空现有数据
        for item in self.卖家列表视图.get_children():
            self.卖家列表视图.delete(item)
        
        # 获取所有卖家
        卖家列表 = self.配置.获取所有卖家()
        
        # 打印调试信息
        self.写入日志(f"共加载了 {len(卖家列表)} 个卖家")
        
        # 检查卖家列表是否为空
        if not 卖家列表:
            self.写入日志("卖家列表为空，请添加卖家")
            return
        
        # 添加卖家数据到Treeview
        for 卖家 in 卖家列表:
            self.卖家列表视图.insert("", tk.END, values=(卖家['卖家名称'], 卖家['卖家ID']))
    
    def 更新状态(self, 消息: str):
        """更新状态（写入日志）"""
        self.写入日志(消息)

    def 显示卖家操作菜单(self, event):
        """显示卖家右键操作菜单"""
        # 获取当前点击的项
        item = self.卖家列表视图.identify_row(event.y)
        if not item:
            return

        # 选中被点击的项
        self.卖家列表视图.selection_set(item)

        # 获取卖家信息
        values = self.卖家列表视图.item(item, "values")
        if not values:
            return

        卖家名称 = values[0]
        卖家ID = values[1]

        # 创建右键菜单
        菜单 = tk.Menu(self.root, tearoff=0)
        菜单.add_command(label="采集", command=lambda: self.开始单个卖家采集(卖家ID))
        菜单.add_separator()
        菜单.add_command(label="删除", command=lambda: self.删除卖家(卖家ID, 卖家名称))
        菜单.add_command(label="设为默认", command=lambda: self.设置为默认卖家(卖家ID, 卖家名称))

        # 显示菜单
        菜单.tk_popup(event.x_root, event.y_root)

    def 删除卖家(self, 卖家ID, 卖家名称):
        """删除卖家"""
        if messagebox.askyesno("确认删除", f"确定要删除卖家 {卖家名称} (ID: {卖家ID}) 吗?"):
            if self.配置.删除卖家信息(卖家ID):
                messagebox.showinfo("成功", f"已删除卖家: {卖家名称}")
                self.刷新卖家列表()
            else:
                messagebox.showerror("错误", "删除卖家失败")

    def 添加卖家(self):
        """添加卖家"""
        卖家ID = self.卖家ID输入.get().strip()
        卖家名称 = self.卖家名称输入.get().strip()

        if not 卖家ID:
            messagebox.showerror("错误", "请输入卖家ID")
            return

        if not 卖家名称:
            messagebox.showerror("错误", "请输入卖家名称")
            return

        if self.配置.保存卖家信息(卖家ID, 卖家名称):
            messagebox.showinfo("成功", f"已添加卖家: {卖家名称} (ID: {卖家ID})")
            self.卖家ID输入.delete(0, tk.END)
            self.卖家名称输入.delete(0, tk.END)
            self.刷新卖家列表()
        else:
            messagebox.showerror("错误", "添加卖家失败")

    def 开始单个卖家采集(self, 卖家ID):
        """开始采集单个卖家的商品"""
        if self.爬取状态:
            messagebox.showinfo("提示", "当前已有采集任务正在运行，请等待完成后再试")
            return

        # 设置采集页数为0（全部页面）
        self.爬取状态 = True
        self.开始按钮.config(state=tk.DISABLED)
        self.停止按钮.config(state=tk.NORMAL)

        self.写入日志(f"开始采集卖家 {卖家ID} 的商品数据...")

        # 创建一个线程来执行采集
        self.爬取线程 = threading.Thread(
            target=self.爬取任务,
            args=([卖家ID], 0, self.只采集有想要.get(), self.只保存ID.get())
        )
        self.爬取线程.daemon = True
        self.爬取线程.start()

    def 开始批量采集(self):
        """开始批量采集"""
        # 检查是否有选中的卖家
        选中项 = self.卖家列表视图.selection()
        if not 选中项:
            messagebox.showwarning("警告", "请先选择要采集的卖家")
            return

        # 获取选中卖家的ID
        卖家ID列表 = []
        for item in 选中项:
            卖家ID = self.卖家列表视图.item(item, "values")[1]
            卖家ID列表.append(卖家ID)

        # 默认参数
        页数 = 0  # 0表示采集全部页面
        只采集有想要 = False
        只保存ID = False

        # 更新界面状态
        self.开始按钮.config(state=tk.DISABLED)
        self.一键采集按钮.config(state=tk.DISABLED)
        self.停止按钮.config(state=tk.NORMAL)

        # 启动爬取任务
        self.爬取线程 = threading.Thread(target=self.爬取任务, args=(卖家ID列表, 页数, 只采集有想要, 只保存ID))
        self.爬取线程.daemon = True
        self.爬取线程.start()

    def 一键采集全部卖家(self):
        """一键采集全部卖家"""
        # 默认参数
        页数 = 0  # 0表示采集全部页面
        只采集有想要 = False
        只保存ID = False

        # 更新界面状态
        self.开始按钮.config(state=tk.DISABLED)
        self.一键采集按钮.config(state=tk.DISABLED)
        self.停止按钮.config(state=tk.NORMAL)

        # 调用主程序的一键采集全部卖家方法
        self.主程序.一键采集全部卖家(页数, 只采集有想要, 只保存ID)

    def 爬取任务(self, 卖家ID列表, 页数, 只采集有想要, 只保存ID):
        """执行爬取任务"""
        self.爬取状态 = True

        try:
            # 调用主程序的批量采集方法
            self.主程序.开始批量采集(卖家ID列表, 页数, 只采集有想要, 只保存ID)
        except Exception as e:
            self.写入日志(f"爬取过程中出现错误: {e}")
        finally:
            self.爬取状态 = False
            # 恢复按钮状态
            self.root.after(0, lambda: self.开始按钮.config(state=tk.NORMAL))
            self.root.after(0, lambda: self.一键采集按钮.config(state=tk.NORMAL))
            self.root.after(0, lambda: self.停止按钮.config(state=tk.DISABLED))

    def 停止采集(self):
        """停止采集"""
        if not self.爬取状态:
            return

        self.爬取状态 = False
        self.主程序.采集状态 = "空闲"  # 直接设置主程序的采集状态
        self.写入日志("正在停止采集...")
        self.开始按钮.config(state=tk.NORMAL)
        self.一键采集按钮.config(state=tk.NORMAL)
        self.停止按钮.config(state=tk.DISABLED)

    def 写入日志(self, 消息: str):
        """写入日志"""
        当前时间 = datetime.now().strftime("%H:%M:%S")
        日志消息 = f"[{当前时间}] {消息}\n"

        self.日志文本框.config(state=tk.NORMAL)
        self.日志文本框.insert(tk.END, 日志消息)
        self.日志文本框.see(tk.END)
        self.日志文本框.config(state=tk.DISABLED)

    def 清空日志(self):
        """清空日志"""
        self.日志文本框.config(state=tk.NORMAL)
        self.日志文本框.delete(1.0, tk.END)
        self.日志文本框.config(state=tk.DISABLED)

    def 打开输出文件夹(self):
        """打开输出文件夹"""
        当前目录 = os.getcwd()
        os.startfile(当前目录)

    def 创建设置页面(self):
        """创建系统设置页面"""
        # 创建主框架
        主框架 = ttk.Frame(self.设置页面, padding="10")
        主框架.pack(fill=tk.BOTH, expand=True)

        # 创建系统设置框架
        系统设置框架 = ttk.LabelFrame(主框架, text="系统设置", padding="10")
        系统设置框架.pack(fill=tk.X, pady=10)

        # 加载当前设置
        系统设置 = self.配置.获取系统设置()

        # 用户代理
        ttk.Label(系统设置框架, text="用户代理:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.用户代理输入 = ttk.Entry(系统设置框架, width=60)
        self.用户代理输入.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        self.用户代理输入.insert(0, 系统设置["用户代理"])

        # 应用密钥
        ttk.Label(系统设置框架, text="应用密钥:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.应用密钥输入 = ttk.Entry(系统设置框架, width=30)
        self.应用密钥输入.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        self.应用密钥输入.insert(0, 系统设置["应用密钥"])

        # 接口名称
        ttk.Label(系统设置框架, text="接口名称:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.接口名称输入 = ttk.Entry(系统设置框架, width=40)
        self.接口名称输入.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        self.接口名称输入.insert(0, 系统设置["接口名称"])

        # 基础URL
        ttk.Label(系统设置框架, text="基础URL:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.基础URL输入 = ttk.Entry(系统设置框架, width=60)
        self.基础URL输入.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        self.基础URL输入.insert(0, 系统设置["基础URL"])

        # Cookie管理框架
        Cookie框架 = ttk.LabelFrame(主框架, text="Cookie管理", padding="10")
        Cookie框架.pack(fill=tk.X, pady=10)

        # 当前Cookie
        ttk.Label(Cookie框架, text="当前Cookie:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.Cookie输入 = Text(Cookie框架, width=60, height=5)
        self.Cookie输入.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        # 安全地插入cookie值
        cookie_value = getattr(self.cookie管理, 'cookie', '') or ''
        self.Cookie输入.insert("1.0", cookie_value)

        # 按钮框架
        按钮框架 = ttk.Frame(主框架)
        按钮框架.pack(fill=tk.X, pady=10)

        保存设置按钮 = ttk.Button(按钮框架, text="保存系统设置", command=self.保存系统设置)
        保存设置按钮.pack(side=tk.LEFT, padx=5)

        保存Cookie按钮 = ttk.Button(按钮框架, text="保存Cookie", command=self.保存Cookie)
        保存Cookie按钮.pack(side=tk.LEFT, padx=5)

        刷新Cookie按钮 = ttk.Button(按钮框架, text="自动刷新Cookie", command=self.刷新Cookie)
        刷新Cookie按钮.pack(side=tk.LEFT, padx=5)

    def 保存系统设置(self):
        """保存系统设置"""
        try:
            self.配置.配置['系统设置']['用户代理'] = self.用户代理输入.get()
            self.配置.配置['系统设置']['应用密钥'] = self.应用密钥输入.get()
            self.配置.配置['系统设置']['接口名称'] = self.接口名称输入.get()
            self.配置.配置['系统设置']['基础URL'] = self.基础URL输入.get()

            self.配置.保存配置()

            # 更新数据采集器的设置
            self.数据采集.用户代理 = self.用户代理输入.get()
            self.数据采集.应用密钥 = self.应用密钥输入.get()
            self.数据采集.接口名称 = self.接口名称输入.get()
            self.数据采集.基础URL = self.基础URL输入.get()

            messagebox.showinfo("成功", "系统设置已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存系统设置失败: {e}")

    def 保存Cookie(self):
        """保存Cookie"""
        try:
            cookie = self.Cookie输入.get("1.0", tk.END).strip()
            if not cookie:
                messagebox.showerror("错误", "Cookie不能为空")
                return

            if self.配置.保存Cookie(cookie):
                self.数据采集.cookie = cookie
                messagebox.showinfo("成功", "Cookie已保存")
            else:
                messagebox.showerror("错误", "保存Cookie失败")
        except Exception as e:
            messagebox.showerror("错误", f"保存Cookie失败: {e}")

    def 刷新Cookie(self):
        """刷新Cookie"""
        # 询问用户选择获取模式
        选择窗口 = tk.Toplevel(self.root)
        选择窗口.title("选择Cookie获取模式")
        选择窗口.geometry("400x200")
        选择窗口.resizable(False, False)
        选择窗口.transient(self.root)
        选择窗口.grab_set()

        # 居中显示
        选择窗口.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))

        # 说明文本
        说明标签 = tk.Label(选择窗口, text="请选择Cookie获取模式：", font=("微软雅黑", 12))
        说明标签.pack(pady=10)

        # 选项说明
        选项框架 = tk.Frame(选择窗口)
        选项框架.pack(pady=10)

        基础说明 = tk.Label(选项框架, text="• 基础模式：仅访问页面，无需登录（推荐）",
                          font=("微软雅黑", 10), anchor="w")
        基础说明.pack(fill="x", padx=20)

        登录说明 = tk.Label(选项框架, text="• 登录模式：需要手动登录，获取完整Cookie",
                          font=("微软雅黑", 10), anchor="w")
        登录说明.pack(fill="x", padx=20)

        # 按钮框架
        按钮框架 = tk.Frame(选择窗口)
        按钮框架.pack(pady=20)

        def 基础模式():
            选择窗口.destroy()
            threading.Thread(target=self._刷新Cookie线程, args=(False,), daemon=True).start()

        def 登录模式():
            选择窗口.destroy()
            threading.Thread(target=self._刷新Cookie线程, args=(True,), daemon=True).start()

        def 取消():
            选择窗口.destroy()

        基础按钮 = ttk.Button(按钮框架, text="基础模式（推荐）", command=基础模式)
        基础按钮.pack(side=tk.LEFT, padx=10)

        登录按钮 = ttk.Button(按钮框架, text="登录模式", command=登录模式)
        登录按钮.pack(side=tk.LEFT, padx=10)

        取消按钮 = ttk.Button(按钮框架, text="取消", command=取消)
        取消按钮.pack(side=tk.LEFT, padx=10)

    def _刷新Cookie线程(self, 需要登录: bool = False):
        """刷新Cookie的线程"""
        try:
            # 更新界面
            模式说明 = "登录模式" if 需要登录 else "基础模式"
            self.root.after(0, lambda: messagebox.showinfo("提示", f"正在启动浏览器获取Cookie（{模式说明}），请稍候..."))

            # 刷新Cookie
            new_cookie = self.cookie管理.刷新Cookie(需要登录=需要登录)

            if new_cookie:
                # 更新界面
                self.root.after(0, lambda: self.Cookie输入.delete("1.0", tk.END))
                self.root.after(0, lambda: self.Cookie输入.insert("1.0", new_cookie))

                # 显示成功信息
                成功信息 = f"Cookie刷新成功！\n模式: {模式说明}\nCookie长度: {len(new_cookie)} 字符"
                if '_m_h5_tk=' in new_cookie:
                    成功信息 += "\n✓ 包含API调用令牌"
                self.root.after(0, lambda: messagebox.showinfo("成功", 成功信息))

                # 更新数据采集器的Cookie
                self.数据采集.cookie = new_cookie
            else:
                失败信息 = f"Cookie刷新失败！\n模式: {模式说明}"
                self.root.after(0, lambda: messagebox.showerror("错误", 失败信息))
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"Cookie刷新过程中出错: {e}"))

    def 显示关于信息(self):
        """显示关于信息"""
        messagebox.showinfo("关于", "闲鱼卖家商品采集工具\n版本: 2.0\n作者: AI助手\n\n功能：\n- 卖家商品采集\n- 系统设置管理")

    def 打开添加卖家窗口(self):
        """打开添加卖家窗口"""
        添加窗口 = tk.Toplevel(self.root)
        添加窗口.title("添加卖家")
        添加窗口.geometry("300x200")
        添加窗口.resizable(False, False)
        添加窗口.transient(self.root)
        添加窗口.grab_set()

        ttk.Label(添加窗口, text="卖家ID:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=10)
        卖家ID = tk.StringVar()
        ttk.Entry(添加窗口, textvariable=卖家ID, width=20).grid(row=0, column=1, padx=10, pady=10)

        ttk.Label(添加窗口, text="卖家名称:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=10)
        卖家名称 = tk.StringVar()
        ttk.Entry(添加窗口, textvariable=卖家名称, width=20).grid(row=1, column=1, padx=10, pady=10)

        ttk.Label(添加窗口, text="分组ID:").grid(row=2, column=0, sticky=tk.W, padx=10, pady=10)
        分组ID = tk.StringVar(value="51959993")
        ttk.Entry(添加窗口, textvariable=分组ID, width=20).grid(row=2, column=1, padx=10, pady=10)

        ttk.Label(添加窗口, text="分组名称:").grid(row=3, column=0, sticky=tk.W, padx=10, pady=10)
        分组名称 = tk.StringVar(value="综合")
        ttk.Entry(添加窗口, textvariable=分组名称, width=20).grid(row=3, column=1, padx=10, pady=10)

        def 确认添加():
            if not 卖家ID.get() or not 卖家名称.get():
                messagebox.showerror("错误", "卖家ID和卖家名称不能为空")
                return

            if self.配置.保存卖家信息(卖家ID.get(), 卖家名称.get()):
                messagebox.showinfo("成功", "添加卖家成功")
                self.刷新卖家列表()
                添加窗口.destroy()
            else:
                messagebox.showerror("错误", "添加卖家失败")

        ttk.Button(添加窗口, text="确认", command=确认添加).grid(row=4, column=1, sticky=tk.E, padx=10, pady=10)

    def 删除当前卖家(self):
        """删除当前卖家"""
        选中项 = self.卖家列表视图.selection()
        if not 选中项:
            messagebox.showerror("错误", "请先选择要删除的卖家")
            return

        # 获取选中的卖家信息
        values = self.卖家列表视图.item(选中项[0], "values")
        卖家名称 = values[0]
        卖家ID = values[1]

        if messagebox.askyesno("确认", f"确定要删除卖家 {卖家名称} (ID: {卖家ID}) 吗?"):
            if self.配置.删除卖家信息(卖家ID):
                messagebox.showinfo("成功", "删除卖家成功")
                self.刷新卖家列表()
            else:
                messagebox.showerror("错误", "删除卖家失败")

    def 设置为默认卖家(self, 卖家ID=None, 卖家名称=None):
        """设置为默认卖家"""
        if 卖家ID is None or 卖家名称 is None:
            选中项 = self.卖家列表视图.selection()
            if not 选中项:
                messagebox.showerror("错误", "请先选择要设为默认的卖家")
                return

            # 获取选中的卖家信息
            values = self.卖家列表视图.item(选中项[0], "values")
            卖家名称 = values[0]
            卖家ID = values[1]

        if self.配置.更新默认设置(卖家ID, 卖家名称):
            messagebox.showinfo("成功", f"已将卖家 {卖家名称}({卖家ID}) 设置为默认卖家")
            self.默认设置 = self.配置.获取默认设置()
        else:
            messagebox.showerror("错误", "设置默认卖家失败")

    def 双击开始采集(self, event):
        """双击卖家列表项开始采集"""
        item = self.卖家列表视图.identify_row(event.y)
        if not item:
            return

        # 获取卖家信息
        values = self.卖家列表视图.item(item, "values")
        if not values:
            return

        卖家ID = values[1]
        self.开始单个卖家采集(卖家ID)


def 创建界面(主程序):
    """创建界面的工厂函数"""
    root = tk.Tk()
    界面 = 闲鱼采集界面(root, 主程序)
    return 界面
