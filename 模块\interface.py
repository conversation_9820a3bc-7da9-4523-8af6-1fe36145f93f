import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, IntVar, BooleanVar, Text
import threading
import time
import random
from typing import List, Dict, Any, Optional
import os
from datetime import datetime
import json

from .config_manager import ConfigManager
from .cookie_manager import <PERSON>ieManager
from .data_collector import DataCollector

class XianyuCollectionInterface:
    def __init__(self, root, main_program=None):
        self.root = root
        self.root.title("闲鱼卖家商品采集工具")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)

        # 设置最小窗口大小，避免布局错乱
        self.root.minsize(800, 600)

        # 初始化变量
        self.main_program = main_program  # 主程序实例的引用

        # 创建样式
        self.create_styles()

        # 如果有主程序实例，使用主程序的组件，否则创建自己的组件
        if main_program:
            self.config = main_program.config_manager
            self.cookie_manager = main_program.cookie_manager
            self.data_collector = main_program.data_collector
        else:
            # 兼容性：如果没有主程序实例，创建自己的组件
            self.config = ConfigManager()
            self.cookie_manager = CookieManager()
            self.data_collector = DataCollector()
        
        self.default_settings = self.config.get_default_settings()
        self.seller_list = self.config.get_seller_list()
        
        self.current_seller_id = tk.StringVar(value=self.default_settings["默认卖家ID"])
        self.current_seller_name = tk.StringVar(value=self.default_settings["默认卖家名称"])
        self.current_group_id = tk.StringVar(value=self.default_settings["默认分组ID"])
        self.current_group_name = tk.StringVar(value=self.default_settings["默认分组名称"])

        # 添加缺失的采集选项变量
        self.only_collect_wanted = BooleanVar(value=False)
        self.only_save_id = BooleanVar(value=False)
        self.use_multithreading = BooleanVar(value=True)  # 默认启用多线程

        self.crawling_status = False
        self.crawling_thread = None
        
        self.create_interface()
        
    def create_styles(self):
        """创建界面样式"""
        style = ttk.Style()
        
        # 创建交替行的样式
        style.configure('EvenRow.TFrame', background='#f0f0f0')
        style.configure('OddRow.TFrame', background='#ffffff')
        
        # 创建按钮样式
        style.configure('采集.TButton', foreground='blue')
        style.configure('删除.TButton', foreground='red')
        
    def create_interface(self):
        self.create_menu_bar()
        
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建选项卡
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 创建各个选项卡页面
        self.collection_page = ttk.Frame(self.notebook)
        self.settings_page = ttk.Frame(self.notebook)
        
        self.notebook.add(self.collection_page, text="数据采集")
        self.notebook.add(self.settings_page, text="系统设置")
        
        # 创建各个页面的内容
        self.create_collection_page()
        self.create_settings_page()
    
    def create_menu_bar(self):
        menu_bar = tk.Menu(self.root)
        self.root.config(menu=menu_bar)
        
        file_menu = tk.Menu(menu_bar, tearoff=0)
        menu_bar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="刷新Cookie", command=self.refresh_cookie)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        
        seller_menu = tk.Menu(menu_bar, tearoff=0)
        menu_bar.add_cascade(label="卖家管理", menu=seller_menu)
        seller_menu.add_command(label="添加卖家", command=self.open_add_seller_window)
        seller_menu.add_command(label="删除卖家", command=self.delete_current_seller)
        seller_menu.add_command(label="设为默认", command=self.set_as_default_seller)
        
        help_menu = tk.Menu(menu_bar, tearoff=0)
        menu_bar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="关于", command=self.show_about_info)
        
    def create_collection_page(self):
        """创建数据采集页面"""
        # 创建主框架
        main_frame = ttk.Frame(self.collection_page, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建卖家管理框架
        seller_frame = ttk.LabelFrame(main_frame, text="卖家管理", padding="10")
        seller_frame.pack(fill=tk.BOTH, expand=False, pady=10)
        
        # 使用Treeview显示卖家列表
        list_frame = ttk.Frame(seller_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 创建Treeview和滚动条
        scrollbar = ttk.Scrollbar(list_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.seller_list_view = ttk.Treeview(list_frame, columns=("卖家名称", "卖家ID"), show="headings", height=6)
        self.seller_list_view.pack(fill=tk.BOTH, expand=True)
        
        # 设置列宽和标题
        self.seller_list_view.column("卖家名称", width=250, anchor=tk.W)
        self.seller_list_view.column("卖家ID", width=150, anchor=tk.W)
        
        self.seller_list_view.heading("卖家名称", text="卖家名称")
        self.seller_list_view.heading("卖家ID", text="卖家ID")
        
        # 设置滚动条
        scrollbar.config(command=self.seller_list_view.yview)
        self.seller_list_view.config(yscrollcommand=scrollbar.set)
        
        # 添加卖家区域
        add_seller_frame = ttk.Frame(seller_frame)
        add_seller_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(add_seller_frame, text="卖家ID:").grid(row=0, column=0, padx=5, pady=5)
        self.seller_id_input = ttk.Entry(add_seller_frame, width=15)
        self.seller_id_input.grid(row=0, column=1, padx=5, pady=5)
        
        ttk.Label(add_seller_frame, text="卖家名称:").grid(row=0, column=2, padx=5, pady=5)
        self.seller_name_input = ttk.Entry(add_seller_frame, width=15)
        self.seller_name_input.grid(row=0, column=3, padx=5, pady=5)
        
        add_button = ttk.Button(add_seller_frame, text="添加卖家", command=self.add_seller)
        add_button.grid(row=0, column=4, padx=10, pady=5)
        
        # 创建日志区域
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=10)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        self.log_text.config(state=tk.DISABLED)
        
        # 创建采集选项区域
        options_frame = ttk.LabelFrame(main_frame, text="采集选项")
        options_frame.pack(fill=tk.X, pady=10)
        
        # 只采集有想要的商品选项
        only_wanted_checkbox = ttk.Checkbutton(options_frame, text="只采集有想要的商品", variable=self.only_collect_wanted)
        only_wanted_checkbox.pack(anchor=tk.W, padx=10, pady=5)
        
        # 只保存ID选项
        only_id_checkbox = ttk.Checkbutton(options_frame, text="只保存商品ID", variable=self.only_save_id)
        only_id_checkbox.pack(anchor=tk.W, padx=10, pady=5)
        
        # 多线程采集选项
        multithreading_checkbox = ttk.Checkbutton(options_frame, text="使用多线程并发采集（推荐，速度更快）", variable=self.use_multithreading)
        multithreading_checkbox.pack(anchor=tk.W, padx=10, pady=5)
        
        # 创建操作按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        self.oneclick_collect_button = ttk.Button(button_frame, text="一键采集全部卖家", command=self.collect_all_sellers_oneclick)
        self.oneclick_collect_button.pack(side=tk.LEFT, padx=5)
        
        self.start_button = ttk.Button(button_frame, text="采集选中卖家", command=self.start_batch_collection)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(button_frame, text="停止采集", command=self.stop_collection, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # 绑定双击事件
        self.seller_list_view.bind("<Double-1>", self.on_seller_double_click)
        
        # 加载卖家列表
        self.load_seller_list()

    def create_settings_page(self):
        """创建设置页面"""
        # 创建主框架
        main_frame = ttk.Frame(self.settings_page, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Cookie设置框架
        cookie_frame = ttk.LabelFrame(main_frame, text="Cookie设置", padding="10")
        cookie_frame.pack(fill=tk.X, pady=10)
        
        # Cookie输入区域
        ttk.Label(cookie_frame, text="Cookie:").pack(anchor=tk.W)
        self.cookie_text = Text(cookie_frame, height=5, wrap=tk.WORD)
        self.cookie_text.pack(fill=tk.X, pady=5)
        
        # Cookie按钮区域
        cookie_button_frame = ttk.Frame(cookie_frame)
        cookie_button_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(cookie_button_frame, text="保存Cookie", command=self.save_cookie).pack(side=tk.LEFT, padx=5)
        ttk.Button(cookie_button_frame, text="自动获取Cookie", command=self.auto_get_cookie).pack(side=tk.LEFT, padx=5)
        ttk.Button(cookie_button_frame, text="测试Cookie", command=self.test_cookie).pack(side=tk.LEFT, padx=5)
        
        # 加载当前Cookie
        self.load_current_cookie()

    def update_status(self, message: str):
        """更新状态信息到日志"""
        self.write_log(message)

    def write_log(self, message: str):
        """写入日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"

        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)

        # 强制更新界面
        self.root.update_idletasks()

    def load_seller_list(self):
        """加载卖家列表"""
        # 清空现有列表
        for item in self.seller_list_view.get_children():
            self.seller_list_view.delete(item)

        # 重新加载卖家列表
        self.seller_list = self.config.get_seller_list()

        for seller in self.seller_list:
            self.seller_list_view.insert("", tk.END, values=(seller["卖家名称"], seller["卖家ID"]))

    def add_seller(self):
        """添加卖家"""
        seller_id = self.seller_id_input.get().strip()
        seller_name = self.seller_name_input.get().strip()

        if not seller_id:
            messagebox.showerror("错误", "请输入卖家ID")
            return

        if not seller_name:
            messagebox.showerror("错误", "请输入卖家名称")
            return

        # 检查是否已存在
        for seller in self.seller_list:
            if seller["卖家ID"] == seller_id:
                messagebox.showerror("错误", "该卖家ID已存在")
                return

        # 添加到配置
        if self.config.add_seller(seller_id, seller_name):
            self.seller_id_input.delete(0, tk.END)
            self.seller_name_input.delete(0, tk.END)
            self.load_seller_list()
            self.write_log(f"已添加卖家: {seller_name} (ID: {seller_id})")
        else:
            messagebox.showerror("错误", "添加卖家失败")

    def on_seller_double_click(self, event):
        """双击卖家列表项"""
        selection = self.seller_list_view.selection()
        if selection:
            item = self.seller_list_view.item(selection[0])
            seller_id = item["values"][1]
            self.start_single_seller_collection(seller_id)

    def start_single_seller_collection(self, seller_id):
        """开始采集单个卖家的商品"""
        if self.crawling_status:
            messagebox.showinfo("提示", "当前已有采集任务正在运行，请等待完成后再试")
            return

        # 设置采集页数为0（全部页面）
        self.crawling_status = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)

        self.write_log(f"开始采集卖家 {seller_id} 的商品数据...")

        # 创建一个线程来执行采集
        self.crawling_thread = threading.Thread(
            target=self.crawling_task,
            args=([seller_id], 0, self.only_collect_wanted.get(), self.only_save_id.get())
        )
        self.crawling_thread.daemon = True
        self.crawling_thread.start()

    def start_batch_collection(self):
        """开始批量采集"""
        if self.crawling_status:
            messagebox.showinfo("提示", "当前已有采集任务正在运行，请等待完成后再试")
            return

        # 获取选中的卖家
        selection = self.seller_list_view.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要采集的卖家")
            return

        seller_id_list = []
        for item in selection:
            seller_id = self.seller_list_view.item(item, "values")[1]
            seller_id_list.append(seller_id)

        # 从界面获取参数
        page_count = 0  # 0表示采集全部页面
        only_collect_wanted = self.only_collect_wanted.get()
        only_save_id = self.only_save_id.get()

        # 更新界面状态
        self.start_button.config(state=tk.DISABLED)
        self.oneclick_collect_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)

        # 启动爬取任务
        use_multithreading = self.use_multithreading.get()
        self.crawling_thread = threading.Thread(target=self.crawling_task, args=(seller_id_list, page_count, only_collect_wanted, only_save_id, use_multithreading))
        self.crawling_thread.daemon = True
        self.crawling_thread.start()

    def collect_all_sellers_oneclick(self):
        """一键采集全部卖家"""
        # 从界面获取参数
        page_count = 0  # 0表示采集全部页面
        only_collect_wanted = self.only_collect_wanted.get()
        only_save_id = self.only_save_id.get()
        use_multithreading = self.use_multithreading.get()

        # 更新界面状态
        self.start_button.config(state=tk.DISABLED)
        self.oneclick_collect_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)

        # 调用主程序的一键采集全部卖家方法
        self.main_program.collect_all_sellers_oneclick(page_count, only_collect_wanted, only_save_id, use_multithreading)

    def crawling_task(self, seller_id_list, page_count, only_collect_wanted, only_save_id, use_multithreading=True):
        """执行爬取任务"""
        self.crawling_status = True

        try:
            # 调用主程序的批量采集方法
            self.main_program.start_batch_collection(seller_id_list, page_count, only_collect_wanted, only_save_id, use_multithreading)
        except Exception as e:
            self.write_log(f"爬取过程中出现错误: {e}")
        finally:
            self.crawling_status = False
            # 恢复按钮状态
            self.root.after(0, self.restore_button_state)

    def restore_button_state(self):
        """恢复按钮状态"""
        self.start_button.config(state=tk.NORMAL)
        self.oneclick_collect_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)

    def stop_collection(self):
        """停止采集"""
        if self.main_program:
            self.main_program.collection_status = "空闲"
        self.crawling_status = False
        self.write_log("用户停止了采集任务")
        self.restore_button_state()

    def refresh_cookie(self):
        """刷新Cookie"""
        self.write_log("开始刷新Cookie...")
        try:
            new_cookie = self.cookie_manager.refresh_cookie()
            if new_cookie:
                self.write_log("Cookie刷新成功")
                self.load_current_cookie()
            else:
                self.write_log("Cookie刷新失败")
        except Exception as e:
            self.write_log(f"Cookie刷新出错: {e}")

    def load_current_cookie(self):
        """加载当前Cookie"""
        try:
            current_cookie = self.cookie_manager.get_cookie()
            self.cookie_text.delete(1.0, tk.END)
            self.cookie_text.insert(1.0, current_cookie)
        except Exception as e:
            self.write_log(f"加载Cookie失败: {e}")

    def save_cookie(self):
        """保存Cookie"""
        cookie = self.cookie_text.get(1.0, tk.END).strip()
        if self.cookie_manager.save_cookie(cookie):
            self.write_log("Cookie保存成功")
            messagebox.showinfo("成功", "Cookie保存成功")
        else:
            self.write_log("Cookie保存失败")
            messagebox.showerror("错误", "Cookie保存失败")

    def auto_get_cookie(self):
        """自动获取Cookie"""
        self.write_log("开始自动获取Cookie...")
        try:
            new_cookie = self.cookie_manager.refresh_cookie()
            if new_cookie:
                self.load_current_cookie()
                self.write_log("自动获取Cookie成功")
                messagebox.showinfo("成功", "自动获取Cookie成功")
            else:
                self.write_log("自动获取Cookie失败")
                messagebox.showerror("错误", "自动获取Cookie失败")
        except Exception as e:
            self.write_log(f"自动获取Cookie出错: {e}")
            messagebox.showerror("错误", f"自动获取Cookie出错: {e}")

    def test_cookie(self):
        """测试Cookie"""
        self.write_log("开始测试Cookie...")
        # 这里可以添加测试Cookie的逻辑
        messagebox.showinfo("提示", "Cookie测试功能待实现")

    def open_add_seller_window(self):
        """打开添加卖家窗口"""
        # 简单实现：聚焦到卖家ID输入框
        self.seller_id_input.focus()

    def delete_current_seller(self):
        """删除当前选中的卖家"""
        selection = self.seller_list_view.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的卖家")
            return

        item = self.seller_list_view.item(selection[0])
        seller_id = item["values"][1]
        seller_name = item["values"][0]

        if messagebox.askyesno("确认", f"确定要删除卖家 {seller_name} (ID: {seller_id}) 吗？"):
            if self.config.delete_seller(seller_id):
                self.load_seller_list()
                self.write_log(f"已删除卖家: {seller_name} (ID: {seller_id})")
            else:
                messagebox.showerror("错误", "删除卖家失败")

    def set_as_default_seller(self):
        """设置为默认卖家"""
        selection = self.seller_list_view.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要设为默认的卖家")
            return

        item = self.seller_list_view.item(selection[0])
        seller_id = item["values"][1]
        seller_name = item["values"][0]

        # 这里可以添加设置默认卖家的逻辑
        self.write_log(f"已设置默认卖家: {seller_name} (ID: {seller_id})")
        messagebox.showinfo("成功", f"已设置默认卖家: {seller_name}")

    def show_about_info(self):
        """显示关于信息"""
        about_text = """
闲鱼卖家商品采集工具 v2.0

功能特点：
- 支持多线程并发采集
- 支持批量采集多个卖家
- 自动Cookie管理
- 实时日志显示

开发者：AI助手
版本：2.0.0
        """
        messagebox.showinfo("关于", about_text)


def create_interface(main_program=None):
    """创建界面的工厂函数"""
    root = tk.Tk()
    interface = XianyuCollectionInterface(root, main_program)
    return interface
